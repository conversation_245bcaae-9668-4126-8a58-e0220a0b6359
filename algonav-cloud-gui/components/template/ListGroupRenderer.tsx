import { Box, Paper, Typography } from '@mui/material'
import { TemplateVariable } from './types/template'
import Input<PERSON>enderer from './InputRenderer'

interface GroupRendererProps {
  groupName: string
  variables: TemplateVariable[]
  template_data?: any
}

// Helper function to get dependent variables for a given variable
function getDependentVariables(variable: TemplateVariable, allVariables: TemplateVariable[]): TemplateVariable[] {
  return allVariables.filter(v => v.gui?.dependson_var === variable.name)
}

// Helper function to group variables with their dependencies
function groupVariablesWithDependencies(variables: TemplateVariable[]): TemplateVariable[][] {
  const groups: TemplateVariable[][] = []
  const processedVars = new Set<string>()

  // Sort variables by order first
  const sortedVariables = [...variables].sort((a, b) =>
    (a.gui?.order ?? Infinity) - (b.gui?.order ?? Infinity)
  )

  // Process each variable and create groups
  function processVariable(variable: TemplateVariable) {
    if (processedVars.has(variable.name)) return

    const group: TemplateVariable[] = [variable]
    processedVars.add(variable.name)

    // Find and add its immediate dependencies
    const dependentVars = getDependentVariables(variable, sortedVariables)
    dependentVars.forEach(depVar => {
      if (!processedVars.has(depVar.name)) {
        group.push(depVar)
        processedVars.add(depVar.name)
      }
    })

    groups.push(group)
  }

  // Process all variables in order
  sortedVariables.forEach(variable => {
    // Only process variables that aren't dependencies of others
    if (!variable.gui?.dependson_var) {
      processVariable(variable)
    }
  })

  return groups
}

export default function ListGroupRenderer({ groupName, variables, template_data }: GroupRendererProps) {
  const variableGroups = groupVariablesWithDependencies(variables)

  return (
    <Paper elevation={2} sx={{ p: 3, bgcolor: '#302c59' }} id={`group-${groupName.toLowerCase().replace(/\s+/g, '-')}`}>
      <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 3 }}>
        {groupName}
      </Typography>

      {variableGroups.map((group, groupIndex) => (
        <Paper
          key={groupIndex}
          elevation={0}
          sx={{
            p: 3,
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2,
            bgcolor: 'background.paper',
            mb: 3
          }}
        >
          {group.map((variable) => (
            <Box key={variable.name}>
              <InputRenderer variable={variable} template_data={template_data} />
            </Box>
          ))}
        </Paper>
      ))}
    </Paper>
  )
}