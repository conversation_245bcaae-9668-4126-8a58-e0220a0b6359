import { Box, Paper, Typography, Accordion, AccordionSummary, AccordionDetails } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { TemplateVariable } from './types/template'
import InputRenderer from './InputRenderer'
import { forwardRef, useImperativeHandle, useState } from 'react'

interface GroupRendererProps {
  groupName: string
  variables: TemplateVariable[]
  template_data?: any
}

export interface GroupRendererRef {
  expandGroup: () => void
  collapseGroup: () => void
  isExpanded: () => boolean
}

// Helper function to get dependent variables for a given variable
function getDependentVariables(variable: TemplateVariable, allVariables: TemplateVariable[]): TemplateVariable[] {
  return allVariables.filter(v => v.gui?.dependson_var === variable.name)
}

// Helper function to group variables with their dependencies
function groupVariablesWithDependencies(variables: TemplateVariable[]): TemplateVariable[][] {
  const groups: TemplateVariable[][] = []
  const processedVars = new Set<string>()

  // Sort variables by order first
  const sortedVariables = [...variables].sort((a, b) =>
    (a.gui?.order ?? Infinity) - (b.gui?.order ?? Infinity)
  )

  // Process each variable and create groups
  function processVariable(variable: TemplateVariable) {
    if (processedVars.has(variable.name)) return

    const group: TemplateVariable[] = [variable]
    processedVars.add(variable.name)

    // Find and add its immediate dependencies
    const dependentVars = getDependentVariables(variable, sortedVariables)
    dependentVars.forEach(depVar => {
      if (!processedVars.has(depVar.name)) {
        group.push(depVar)
        processedVars.add(depVar.name)
      }
    })

    groups.push(group)
  }

  // Process all variables in order
  sortedVariables.forEach(variable => {
    // Only process variables that aren't dependencies of others
    if (!variable.gui?.dependson_var) {
      processVariable(variable)
    }
  })

  return groups
}

const ListGroupRenderer = forwardRef<GroupRendererRef, GroupRendererProps>(
  ({ groupName, variables, template_data }, ref) => {
    const [isExpanded, setIsExpanded] = useState(false) // Default to collapsed
    const variableGroups = groupVariablesWithDependencies(variables)

    useImperativeHandle(ref, () => ({
      expandGroup: () => setIsExpanded(true),
      collapseGroup: () => setIsExpanded(false),
      isExpanded: () => isExpanded,
    }))

    return (
      <Box
        id={`group-${groupName.toLowerCase().replace(/\s+/g, '-')}`}
        sx={{ mb: 5 }}
      >
        <Accordion
          expanded={isExpanded}
          onChange={(_, expanded) => setIsExpanded(expanded)}
          className="template-group-accordion"
          sx={{
            // Specific styling for template group accordions only
            '&.template-group-accordion': {
              bgcolor: '#302c59',
              borderRadius: 2,
              overflow: 'hidden',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              '&:before': {
                display: 'none', // Remove default MUI accordion divider
              },
              '& .MuiAccordionSummary-root': {
                bgcolor: '#302c59',
                color: 'white',
                minHeight: 64,
                px: 3,
                py: 2,
                transition: 'background-color 0.2s ease',
                '&:hover': {
                  bgcolor: '#3a3567',
                },
                '&.Mui-expanded': {
                  minHeight: 64,
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                },
              },
              '& .MuiAccordionSummary-content': {
                margin: 0,
                '&.Mui-expanded': {
                  margin: 0,
                },
              },
              '& .MuiAccordionSummary-expandIconWrapper': {
                color: 'white',
                transition: 'transform 0.2s ease, color 0.2s ease',
                '&:hover': {
                  color: '#e0e0e0',
                },
                '&.Mui-expanded': {
                  transform: 'rotate(180deg)',
                },
              },
              '& .MuiAccordionDetails-root': {
                bgcolor: '#302c59',
                p: 3,
                pt: 2,
              },
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
          >
            <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'white' }}>
              {groupName}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mt: 1 }}>
              {variableGroups.map((group, groupIndex) => (
                <Paper
                  key={groupIndex}
                  elevation={0}
                  sx={{
                    p: 3,
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2,
                    bgcolor: 'background.paper',
                    mb: groupIndex < variableGroups.length - 1 ? 3 : 0,
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
                  }}
                >
                  {group.map((variable) => (
                    <Box key={variable.name}>
                      <InputRenderer variable={variable} template_data={template_data} />
                    </Box>
                  ))}
                </Paper>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    )
  }
)

ListGroupRenderer.displayName = 'ListGroupRenderer'

export default ListGroupRenderer