import {
  Box,
  Paper,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Avatar,
  Chip,
  Fade
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import SettingsIcon from '@mui/icons-material/Settings'
import GpsFixedIcon from '@mui/icons-material/GpsFixed'
import SpeedIcon from '@mui/icons-material/Speed'
import RotateRightIcon from '@mui/icons-material/RotateRight'
import OutputIcon from '@mui/icons-material/Output'
import FolderIcon from '@mui/icons-material/Folder'
import { TemplateVariable } from './types/template'
import InputRenderer from './InputRenderer'
import { forwardRef, useImperativeHandle, useState } from 'react'

interface GroupRendererProps {
  groupName: string
  variables: TemplateVariable[]
  template_data?: any
}

export interface GroupRendererRef {
  expandGroup: () => void
  collapseGroup: () => void
  isExpanded: () => boolean
}

// Helper function to get appropriate icon for each group
function getGroupIcon(groupName: string) {
  const name = groupName.toLowerCase()
  if (name.includes('gnss') || name.includes('position')) return GpsFixedIcon
  if (name.includes('imu') || name.includes('motion')) return RotateRightIcon
  if (name.includes('velocity') || name.includes('speed')) return SpeedIcon
  if (name.includes('output')) return OutputIcon
  if (name.includes('constraint')) return SettingsIcon
  return FolderIcon // Default icon
}

// Helper function to get group color theme
function getGroupTheme(groupName: string) {
  const name = groupName.toLowerCase()
  if (name.includes('gnss') || name.includes('position')) return '#4CAF50' // Green
  if (name.includes('imu') || name.includes('motion')) return '#2196F3' // Blue
  if (name.includes('velocity') || name.includes('speed')) return '#FF9800' // Orange
  if (name.includes('output')) return '#9C27B0' // Purple
  if (name.includes('constraint')) return '#F44336' // Red
  return '#FC6200' // Default AlgoNav orange
}

// Helper function to get dependent variables for a given variable
function getDependentVariables(variable: TemplateVariable, allVariables: TemplateVariable[]): TemplateVariable[] {
  return allVariables.filter(v => v.gui?.dependson_var === variable.name)
}

// Helper function to group variables with their dependencies
function groupVariablesWithDependencies(variables: TemplateVariable[]): TemplateVariable[][] {
  const groups: TemplateVariable[][] = []
  const processedVars = new Set<string>()

  // Sort variables by order first
  const sortedVariables = [...variables].sort((a, b) =>
    (a.gui?.order ?? Infinity) - (b.gui?.order ?? Infinity)
  )

  // Process each variable and create groups
  function processVariable(variable: TemplateVariable) {
    if (processedVars.has(variable.name)) return

    const group: TemplateVariable[] = [variable]
    processedVars.add(variable.name)

    // Find and add its immediate dependencies
    const dependentVars = getDependentVariables(variable, sortedVariables)
    dependentVars.forEach(depVar => {
      if (!processedVars.has(depVar.name)) {
        group.push(depVar)
        processedVars.add(depVar.name)
      }
    })

    groups.push(group)
  }

  // Process all variables in order
  sortedVariables.forEach(variable => {
    // Only process variables that aren't dependencies of others
    if (!variable.gui?.dependson_var) {
      processVariable(variable)
    }
  })

  return groups
}

const ListGroupRenderer = forwardRef<GroupRendererRef, GroupRendererProps>(
  ({ groupName, variables, template_data }, ref) => {
    const [isExpanded, setIsExpanded] = useState(false) // Default to collapsed
    const variableGroups = groupVariablesWithDependencies(variables)

    // Get dynamic styling for this group
    const GroupIcon = getGroupIcon(groupName)
    const groupTheme = getGroupTheme(groupName)
    const fieldCount = variables.length

    useImperativeHandle(ref, () => ({
      expandGroup: () => setIsExpanded(true),
      collapseGroup: () => setIsExpanded(false),
      isExpanded: () => isExpanded,
    }))

    return (
      <Box
        id={`group-${groupName.toLowerCase().replace(/\s+/g, '-')}`}
        sx={{ mb: 4 }}
      >
        <Accordion
          expanded={isExpanded}
          onChange={(_, expanded) => setIsExpanded(expanded)}
          className="template-group-accordion"
          sx={{
            // Specific styling for template group accordions only
            '&.template-group-accordion': {
              bgcolor: '#302c59',
              borderRadius: 3,
              overflow: 'hidden',
              boxShadow: isExpanded
                ? '0 8px 24px rgba(48, 44, 89, 0.2)'
                : '0 4px 12px rgba(48, 44, 89, 0.12)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              transition: 'box-shadow 0.2s ease, transform 0.2s ease',
              position: 'relative',
              '&:before': {
                display: 'none', // Remove default MUI accordion divider
              },
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: '0 8px 32px rgba(48, 44, 89, 0.18)',
              },
              // Orange accent border on the left
              '&::after': {
                content: '""',
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: 4,
                background: groupTheme,
                borderRadius: '0 0 0 12px',
                zIndex: 1,
              },
              '& .MuiAccordionSummary-root': {
                bgcolor: 'transparent',
                color: 'white',
                minHeight: 80,
                px: 3,
                py: 2,
                transition: 'background-color 0.15s ease',
                position: 'relative',
                zIndex: 2,
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.05)',
                },
                '&.Mui-expanded': {
                  minHeight: 80,
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                },
              },
              '& .MuiAccordionSummary-content': {
                margin: 0,
                alignItems: 'center',
                '&.Mui-expanded': {
                  margin: 0,
                },
              },
              '& .MuiAccordionSummary-expandIconWrapper': {
                color: 'white',
                transition: 'transform 0.2s ease, color 0.15s ease',
                '&:hover': {
                  color: '#e0e0e0',
                },
                '&.Mui-expanded': {
                  transform: 'rotate(180deg)',
                },
              },
              '& .MuiAccordionDetails-root': {
                bgcolor: '#302c59',
                p: 0,
                position: 'relative',
                zIndex: 2,
              },
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', pl: 1 }}>
              <Avatar
                sx={{
                  bgcolor: groupTheme,
                  mr: 3,
                  width: 48,
                  height: 48,
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
                  transition: 'transform 0.15s ease',
                  '&:hover': {
                    transform: 'scale(1.02)',
                  }
                }}
              >
                <GroupIcon sx={{ fontSize: 24 }} />
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography
                  variant="h6"
                  sx={{
                    color: 'white',
                    fontWeight: 600,
                    fontSize: '1.125rem',
                    lineHeight: 1.2,
                    mb: 0.5
                  }}
                >
                  {groupName}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: 'rgba(255,255,255,0.7)',
                    fontSize: '0.75rem',
                    fontWeight: 400
                  }}
                >
                  {fieldCount} field{fieldCount !== 1 ? 's' : ''} • {isExpanded ? 'Expanded' : 'Collapsed'}
                </Typography>
              </Box>
              <Fade in={!isExpanded}>
                <Chip
                  label="Configure"
                  size="small"
                  sx={{
                    mr: 2,
                    bgcolor: 'rgba(255,255,255,0.15)',
                    color: 'white',
                    fontWeight: 500,
                    '&:hover': {
                      bgcolor: 'rgba(255,255,255,0.25)',
                    }
                  }}
                />
              </Fade>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ p: 4, pt: 3 }}>
              {variableGroups.map((group, groupIndex) => (
                <Paper
                  key={groupIndex}
                  elevation={0}
                  sx={{
                    p: 4,
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: 3,
                    bgcolor: 'background.paper',
                    mb: groupIndex < variableGroups.length - 1 ? 3 : 0,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                    transition: 'box-shadow 0.15s ease, transform 0.15s ease',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
                      transform: 'translateY(-1px)',
                    },
                    // Subtle top accent line
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: 2,
                      bgcolor: groupTheme,
                      opacity: 0.8,
                    }
                  }}
                >
                  {group.map((variable) => (
                    <Box
                      key={variable.name}
                      sx={{
                        '&:not(:last-child)': {
                          mb: 3,
                          pb: 3,
                          borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                        }
                      }}
                    >
                      <InputRenderer variable={variable} template_data={template_data} />
                    </Box>
                  ))}
                </Paper>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    )
  }
)

ListGroupRenderer.displayName = 'ListGroupRenderer'

export default ListGroupRenderer