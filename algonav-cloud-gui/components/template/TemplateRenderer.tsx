import { useEffect } from 'react'
import { useMergedTemplateVars } from '../../lib/hooks/useTemplateVars'
import { useTemplateStore } from '../../lib/stores/templateStore'
import ListGroupRenderer from './ListGroupRenderer'
import { TemplateVariable } from './types/template'
import { Box, CircularProgress, Alert, Stack, Typography } from '@mui/material'

interface TemplateRendererProps {
  templateId: string;
  templateName?: string;
}

export default function TemplateRenderer({ templateId, templateName }: TemplateRendererProps) {
  const { mergedVars, isLoading, template_data } = useMergedTemplateVars(templateId)
  const { setTemplateVars, templateVars } = useTemplateStore()

  useEffect(() => {
    if (mergedVars.length > 0) {
      setTemplateVars(mergedVars)
    }
  }, [mergedVars, setTemplateVars])

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <CircularProgress />
      </Box>
    )
  }

  if (!templateVars.length) {
    return (
      <Alert severity="error">
        Error loading template variables
      </Alert>
    )
  }

  const groupedVariables = templateVars.reduce((groups: { [key: string]: TemplateVariable[] }, variable) => {
    if (!variable.gui?.group) return groups;

    const group = variable.gui.group
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(variable)
    return groups
  }, {})

  // Sort groups by the minimum order in each group
  const sortedGroups = Object.entries(groupedVariables)
    .sort(([groupA, varsA], [groupB, varsB]) => {
      const minOrderA = Math.min(...varsA.map(v => v.gui?.order ?? Infinity))
      const minOrderB = Math.min(...varsB.map(v => v.gui?.order ?? Infinity))
      return minOrderA - minOrderB
    })

  return (
    <Stack spacing={5}>
      {templateName && (
        <Typography variant="h4" component="h2" gutterBottom>
          {templateName}
        </Typography>
      )}
      {sortedGroups.map(([groupName, variables]) => (
        <ListGroupRenderer
          key={groupName}
          groupName={groupName}
          variables={variables}
          template_data={template_data}
        />
      ))}
    </Stack>
  )
}