// components/inputs/LogFieldSelector.tsx
import { useState, useEffect } from 'react';
import { 
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Typography,
  TextField,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  InputAdornment
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';

interface LogField {
  field: string;
  caption: string;
  precision: string;
  settings: Array<{
    name: string;
    data: any;
    gui: {
      component_id: string;
    };
  }>;
}

interface LogGroup {
  name: string;
  label: string;
  logprefix: string;
  fields: LogField[];
}

interface LogFieldSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (selectedFields: Array<{field: string, logprefix: string, caption: string}>) => void;
  logGroups: LogGroup[];
}

export default function LogFieldSelector({
  open,
  onClose,
  onSelect,
  logGroups,
}: LogFieldSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFields, setSelectedFields] = useState<Array<{field: string, logprefix: string, caption: string}>>([]);
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  // Reset selection when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedFields([]);
      setSearchTerm('');
      // Expand all groups by default
      setExpandedGroups(logGroups.map(group => group.name));
    }
  }, [open, logGroups]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    
    // If search term is not empty, expand all groups
    if (event.target.value) {
      setExpandedGroups(logGroups.map(group => group.name));
    }
  };

  const handleToggleGroup = (groupName: string) => {
    setExpandedGroups(prev => 
      prev.includes(groupName)
        ? prev.filter(name => name !== groupName)
        : [...prev, groupName]
    );
  };

  const handleToggleField = (field: string, logprefix: string, caption: string) => {
    setSelectedFields(prev => {
      const isSelected = prev.some(f => f.field === field && f.logprefix === logprefix);
      
      if (isSelected) {
        return prev.filter(f => !(f.field === field && f.logprefix === logprefix));
      } else {
        return [...prev, { field, logprefix, caption }];
      }
    });
  };

  const handleConfirm = () => {
    onSelect(selectedFields);
    onClose();
  };

  // Filter log groups and fields based on search term
  const filteredLogGroups = logGroups.map(group => {
    const filteredFields = group.fields.filter(field => 
      field.caption.toLowerCase().includes(searchTerm.toLowerCase()) ||
      field.field.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return {
      ...group,
      fields: filteredFields,
      hasMatches: filteredFields.length > 0
    };
  }).filter(group => group.hasMatches || searchTerm === '');

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>Select Log Fields</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            placeholder="Search fields..."
            value={searchTerm}
            onChange={handleSearchChange}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>
        
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Selected: {selectedFields.length} field(s)
        </Typography>
        
        <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
          {filteredLogGroups.map((group) => (
            <Accordion 
              key={group.name}
              expanded={expandedGroups.includes(group.name)}
              onChange={() => handleToggleGroup(group.name)}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">{group.label}</Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: 0 }}>
                <List dense>
                  {group.fields.map((field) => {
                    const isSelected = selectedFields.some(
                      f => f.field === field.field && f.logprefix === group.logprefix
                    );
                    
                    return (
                      <ListItem 
                        key={`${group.logprefix}-${field.field}`}
                        button
                        onClick={() => handleToggleField(field.field, group.logprefix, field.caption)}
                      >
                        <ListItemIcon>
                          <Checkbox
                            edge="start"
                            checked={isSelected}
                            tabIndex={-1}
                            disableRipple
                          />
                        </ListItemIcon>
                        <ListItemText 
                          primary={field.caption}
                          secondary={field.field}
                        />
                      </ListItem>
                    );
                  })}
                </List>
              </AccordionDetails>
            </Accordion>
          ))}
          
          {filteredLogGroups.length === 0 && (
            <Typography variant="body2" sx={{ p: 2, textAlign: 'center' }}>
              No matching fields found.
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleConfirm}
          variant="contained"
          disabled={selectedFields.length === 0}
        >
          Add Selected ({selectedFields.length})
        </Button>
      </DialogActions>
    </Dialog>
  );
}
