// components/inputs/logconfig/LogConfigFieldSelector.tsx
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Grid,
  Card,
  CardHeader,
  CardContent,
  TextField,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from '@mui/material';
import {
  DragDropContext,
  Draggable,
  DropResult,
  Droppable,
  DraggableProvided,
  DroppableProvided,
  DroppableStateSnapshot,
  DroppableProps,
} from 'react-beautiful-dnd';
import SearchIcon from '@mui/icons-material/Search';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  LogField,
  DatabaseLogField,
  FilteredLogGroup,
  LogFieldChangeHandler,
  LogFieldEditHandler,
  LogFieldRemoveHandler,
} from '../types/LogConfigTypes';
import { useDebouncedValue } from '@/lib/hooks/useDebouncedValue';
import { usePerformanceMonitor } from '@/lib/hooks/usePerformanceMonitor';

/** Strict-mode-compatible Droppable wrapper */
const StrictModeDroppable: React.FC<DroppableProps> = ({
  children,
  ...props
}) => {
  const [enabled, setEnabled] = useState(false);

  useEffect(() => {
    const animation = requestAnimationFrame(() => setEnabled(true));
    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, []);

  if (!enabled) return null;
  return <Droppable {...props}>{children}</Droppable>;
};

/** Custom drag preview styling to ensure visibility */
const dragPreviewStyles = `
  [data-rbd-drag-handle-draggable-id] {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
  }

  [data-rbd-draggable-context-id] {
    opacity: 1 !important;
    visibility: visible !important;
  }

  [data-rbd-drag-handle-context-id] {
    opacity: 1 !important;
    visibility: visible !important;
  }

  .react-beautiful-dnd-dragging {
    opacity: 1 !important;
    visibility: visible !important;
    background: white !important;
    border: 2px solid #1976d2 !important;
    border-radius: 4px !important;
    box-shadow: 0 8px 16px rgba(0,0,0,0.15) !important;
    z-index: 9999 !important;
  }
`;

// Inject drag preview styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = dragPreviewStyles;
  if (!document.head.querySelector('style[data-drag-preview]')) {
    styleElement.setAttribute('data-drag-preview', 'true');
    document.head.appendChild(styleElement);
  }
}

interface LogConfigFieldSelectorProps {
  availableFields: FilteredLogGroup[];
  selectedFields: LogField[];
  onFieldAdd: (field: DatabaseLogField, groupName: string, logprefix: string, index?: number) => void;
  onFieldRemove: LogFieldRemoveHandler;
  onFieldEdit: LogFieldEditHandler;
  onFieldReorder: (result: DropResult) => void;
  isLoading?: boolean;
  error?: any;
  searchTerm?: string;
  expandedGroups?: string[];
  onSearchChange?: (term: string) => void;
  onToggleGroup?: (groupName: string) => void;
}

const LogConfigFieldSelector = React.memo(function LogConfigFieldSelector({
  availableFields,
  selectedFields,
  onFieldAdd,
  onFieldRemove,
  onFieldEdit,
  onFieldReorder,
  isLoading = false,
  error = null,
  searchTerm = '',
  expandedGroups = [],
  onSearchChange,
  onToggleGroup,
}: LogConfigFieldSelectorProps) {
  // Performance monitoring
  usePerformanceMonitor('LogConfigFieldSelector');

  // Local state fallback if not provided by parent
  const [localSearchTerm, setLocalSearchTerm] = useState('');
  const [localExpandedGroups, setLocalExpandedGroups] = useState<string[]>([]);

  // Use provided state or fallback to local state
  const currentSearchTerm = onSearchChange ? searchTerm : localSearchTerm;
  const currentExpandedGroups = onToggleGroup ? expandedGroups : localExpandedGroups;

  // Debounce the search term for filtering (300ms delay)
  const debouncedSearchTerm = useDebouncedValue(currentSearchTerm, 300);

  // Initialize expanded groups when available fields change (only for local state)
  useEffect(() => {
    if (!onToggleGroup) {
      setLocalExpandedGroups(availableFields.map(g => g.name));
    }
  }, [availableFields, onToggleGroup]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (onSearchChange) {
        onSearchChange(value);
        if (value && onToggleGroup) {
          // Expand all groups when searching
          availableFields.forEach(g => {
            if (!expandedGroups.includes(g.name)) {
              onToggleGroup(g.name);
            }
          });
        }
      } else {
        setLocalSearchTerm(value);
        if (value) {
          setLocalExpandedGroups(availableFields.map(g => g.name));
        }
      }
    },
    [onSearchChange, onToggleGroup, availableFields, expandedGroups],
  );

  const handleToggleGroup = useCallback((g: string) => {
    if (onToggleGroup) {
      onToggleGroup(g);
    } else {
      setLocalExpandedGroups((prev) =>
        prev.includes(g) ? prev.filter((n) => n !== g) : [...prev, g],
      );
    }
  }, [onToggleGroup]);

  // Filter available fields based on debounced search term with memoization
  const filteredAvailableFields = useMemo(() => {
    return availableFields.map(group => ({
      ...group,
      fields: debouncedSearchTerm
        ? group.fields.filter(
            (f: DatabaseLogField) =>
              f.caption.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
              f.field.toLowerCase().includes(debouncedSearchTerm.toLowerCase()),
          )
        : group.fields,
    })).filter(group => group.fields.length > 0);
  }, [availableFields, debouncedSearchTerm]);



  return (
    <DragDropContext
      onDragEnd={(result) => {
        // Call the original handler
        onFieldReorder(result);
      }}
    >
      <Box>
        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
          Field Selection
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Select and configure the data fields to include in your log output. Drag fields from the available list to add them, or use the arrow button.
        </Typography>
      </Box>
      <Grid container spacing={3} sx={{ minHeight: 500 }}>
        {/* Available fields */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%', boxShadow: 1 }}>
            <CardHeader
              title="Available Fields"
              action={
                <TextField
                  placeholder="Search fields…"
                  size="small"
                  value={currentSearchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ width: 200 }}
                />
              }
            />
            <Divider />
            <CardContent
              sx={{
                p: 0,
                height: 400,
                overflow: 'auto',
                // Prevent scroll issues during drag
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '4px',
                },
              }}
            >
              <StrictModeDroppable droppableId="availableFields" isDropDisabled>
                {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      minHeight: 100,
                      background: snapshot.isDraggingOver
                        ? 'rgba(63,81,181,.08)'
                        : 'transparent',
                      transition: 'background-color .2s',
                    }}
                  >
                    {isLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                        <CircularProgress />
                      </Box>
                    ) : error ? (
                      <Box sx={{ p: 2 }}>
                        <Typography color="error">Error loading log fields</Typography>
                      </Box>
                    ) : filteredAvailableFields.map((g) => (
                      <Accordion
                        key={g.name}
                        expanded={currentExpandedGroups.includes(g.name)}
                        onChange={() => handleToggleGroup(g.name)}
                        disableGutters
                        elevation={0}
                        square
                        sx={{
                          // Complete style reset with maximum specificity to prevent cascade
                          backgroundColor: '#ffffff !important',
                          color: '#000000 !important',
                          boxShadow: 'none !important',
                          borderRadius: '0 !important',
                          border: 'none !important',
                          transform: 'none !important',
                          background: 'none !important',
                          '&:before': {
                            display: 'block !important',
                            backgroundColor: 'rgba(0, 0, 0, 0.12) !important',
                          },
                          '&:after': {
                            display: 'none !important',
                          },
                          '&:hover': {
                            transform: 'none !important',
                            boxShadow: 'none !important',
                            backgroundColor: '#ffffff !important',
                          },
                          '& *': {
                            color: 'inherit !important',
                          },
                          '& .MuiAccordionSummary-root': {
                            backgroundColor: '#ffffff !important',
                            color: '#000000 !important',
                            minHeight: '48px !important',
                            padding: '8px 16px !important',
                            transition: 'background-color 0.2s ease !important',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.04) !important',
                              transform: 'none !important',
                            },
                            '&.Mui-expanded': {
                              minHeight: '48px !important',
                              borderBottom: 'none !important',
                            },
                            '& *': {
                              color: '#000000 !important',
                            },
                          },
                          '& .MuiAccordionSummary-content': {
                            margin: '12px 0 !important',
                            '&.Mui-expanded': {
                              margin: '12px 0 !important',
                            },
                            '& *': {
                              color: '#000000 !important',
                            },
                          },
                          '& .MuiAccordionSummary-expandIconWrapper': {
                            color: 'rgba(0, 0, 0, 0.54) !important',
                            transition: 'transform 0.2s ease !important',
                            '&:hover': {
                              color: '#000000 !important',
                              transform: 'none !important',
                            },
                            '&.Mui-expanded': {
                              transform: 'rotate(180deg) !important',
                            },
                          },
                          '& .MuiAccordionDetails-root': {
                            backgroundColor: '#ffffff !important',
                            color: '#000000 !important',
                            padding: '0 !important',
                            '& *': {
                              color: 'inherit !important',
                            },
                          },
                          '& .MuiTypography-root': {
                            color: '#000000 !important',
                          },
                          '& .MuiListItemText-primary': {
                            color: '#000000 !important',
                          },
                          '& .MuiListItemText-secondary': {
                            color: 'rgba(0, 0, 0, 0.6) !important',
                          },
                          '& .MuiListItem-root': {
                            color: '#000000 !important',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.04) !important',
                            },
                          },
                        }}
                      >
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>{g.label}</Typography>
                        </AccordionSummary>
                        <AccordionDetails
                          sx={{
                            p: 0,
                            // Stabilize layout during drag operations
                            '& .MuiList-root': {
                              // Prevent layout shifts
                              minHeight: 'auto',
                            }
                          }}
                        >
                          <List
                            dense
                            sx={{
                              // Ensure consistent spacing during drag
                              '& .MuiListItem-root': {
                                minHeight: 48, // Fixed height to prevent layout shifts
                              }
                            }}
                          >
                            {g.fields.map((f: DatabaseLogField, fi: number) => {
                              const added = selectedFields.some(
                                (sel: LogField) =>
                                  sel.field === f.field && sel.logprefix === g.logprefix,
                              );
                              return (
                                <Draggable
                                  key={`${g.name}|${fi}|${g.logprefix}`}
                                  draggableId={`${g.name}|${fi}|${g.logprefix}`}
                                  index={fi}
                                  isDragDisabled={false}
                                >
                                  {(prov: DraggableProvided, snapshot) => (
                                    <ListItem
                                      ref={prov.innerRef}
                                      {...prov.draggableProps}
                                      {...prov.dragHandleProps}
                                      secondaryAction={
                                        <IconButton
                                          size="small"
                                          edge="end"
                                          onClick={() =>
                                            onFieldAdd(f, g.name, g.logprefix)
                                          }
                                        >
                                          <ArrowForwardIcon />
                                        </IconButton>
                                      }
                                      sx={{
                                        cursor: snapshot.isDragging ? 'grabbing' : 'grab',
                                        // Ensure drag preview is visible
                                        opacity: snapshot.isDragging ? 0.5 : 1, // Make original semi-transparent during drag
                                        ...(added && {
                                          borderLeft: '4px solid',
                                          borderColor: 'success.main',
                                          pl: 2,
                                          backgroundColor: 'rgba(76,175,80,0.08)',
                                        })
                                      }}
                                      style={{
                                        // Let react-beautiful-dnd handle the drag preview styling
                                        ...prov.draggableProps.style,
                                      }}
                                    >
                                      <DragIndicatorIcon
                                        fontSize="small"
                                        sx={{
                                          color: 'text.secondary',
                                          mr: 1,
                                          opacity: 0.7
                                        }}
                                      />
                                      <ListItemText
                                        primary={f.caption}
                                        secondary={f.field}
                                        primaryTypographyProps={{
                                          variant: 'body2',
                                          color: 'text.primary',
                                        }}
                                        secondaryTypographyProps={{
                                          variant: 'caption',
                                          color: 'text.secondary',
                                        }}
                                      />
                                    </ListItem>
                                  )}
                                </Draggable>
                              );
                            })}
                          </List>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </StrictModeDroppable>
            </CardContent>
          </Card>
        </Grid>

        {/* Selected fields */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%', boxShadow: 1 }}>
            <CardHeader
              title="Selected Fields"
              subheader={`${selectedFields.length} field(s) selected`}
              sx={{ pb: 1 }}
            />
            <Divider />
            <CardContent
              sx={{
                p: 0,
                height: 400,
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column',
                // Prevent scroll issues during drag
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '4px',
                },
              }}
            >
              <StrictModeDroppable droppableId="selectedFields">
                {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      flexGrow: 1,
                      minHeight: 100,
                      background: snapshot.isDraggingOver
                        ? 'rgba(76,175,80,.08)'
                        : 'transparent',
                      transition: 'background-color .2s',
                    }}
                  >
                    {selectedFields.length === 0 ? (
                      <Box p={4} textAlign="center" sx={{ borderRadius: 1, backgroundColor: 'grey.50', m: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                          No fields selected. Drag fields from the left panel or use the arrow buttons to add them.
                        </Typography>
                      </Box>
                    ) : (
                      <List
                        dense
                        sx={{
                          // Ensure consistent spacing during drag
                          '& .MuiListItem-root': {
                            minHeight: 64, // Fixed height to prevent layout shifts
                          }
                        }}
                      >
                        {selectedFields.map((f: LogField, idx: number) => (
                          <Draggable
                            key={`selected-${f.logprefix || ''}-${f.field}-${idx}`}
                            draggableId={`selected-${f.logprefix || ''}-${f.field}-${idx}`}
                            index={idx}
                            isDragDisabled={false}
                          >
                            {(prov: DraggableProvided, snapshot) => (
                              <ListItem
                                ref={prov.innerRef}
                                {...prov.draggableProps}
                                divider={idx < selectedFields.length - 1}
                                sx={{
                                  // Ensure drag preview is visible
                                  opacity: snapshot.isDragging ? 0.5 : 1, // Make original semi-transparent during drag
                                }}
                                style={{
                                  // Let react-beautiful-dnd handle the drag preview styling
                                  ...prov.draggableProps.style,
                                }}
                              >
                                <Box
                                  {...prov.dragHandleProps}
                                  sx={{
                                    flexGrow: 1,
                                    cursor: 'grab',
                                    display: 'flex',
                                    alignItems: 'center'
                                  }}
                                >
                                  <DragIndicatorIcon
                                    fontSize="small"
                                    sx={{
                                      color: 'text.secondary',
                                      mr: 1,
                                      opacity: 0.7
                                    }}
                                  />
                                  <ListItemText
                                    primary={f.caption}
                                    secondary={
                                      <Box component="span">
                                        {f.logprefix && (
                                          <Typography
                                            component="span"
                                            variant="caption"
                                            color="primary"
                                            sx={{ mr: 1 }}
                                          >
                                            {f.logprefix}:
                                          </Typography>
                                        )}
                                        {f.field}
                                      </Box>
                                    }
                                  />
                                </Box>

                                <Box onClick={(e) => e.stopPropagation()}>
                                  <IconButton
                                    size="small"
                                    sx={{ mr: 1 }}
                                    onClick={() => onFieldEdit(f, idx)}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => onFieldRemove(idx)}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Box>
                              </ListItem>
                            )}
                          </Draggable>
                        ))}
                      </List>
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </StrictModeDroppable>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </DragDropContext>
  );
});

// Memoization comparison function for better performance
LogConfigFieldSelector.displayName = 'LogConfigFieldSelector';

export default LogConfigFieldSelector;