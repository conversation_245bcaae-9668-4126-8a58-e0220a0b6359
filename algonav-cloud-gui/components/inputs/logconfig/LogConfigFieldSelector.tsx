// components/inputs/logconfig/LogConfigFieldSelector.tsx
import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  Grid,
  Card,
  CardHeader,
  CardContent,
  TextField,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from '@mui/material';
import {
  DragDropContext,
  Draggable,
  DropResult,
  Droppable,
  DraggableProvided,
  DroppableProvided,
  DroppableStateSnapshot,
  DroppableProps,
} from 'react-beautiful-dnd';
import SearchIcon from '@mui/icons-material/Search';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  LogField,
  DatabaseLogField,
  FilteredLogGroup,
  LogFieldChangeHandler,
  LogFieldEditHandler,
  LogFieldRemoveHandler,
} from '../types/LogConfigTypes';
import { useDebouncedValue } from '@/lib/hooks/useDebouncedValue';
import { usePerformanceMonitor } from '@/lib/hooks/usePerformanceMonitor';

/** Strict-mode-compatible Droppable wrapper */
const StrictModeDroppable: React.FC<DroppableProps> = ({
  children,
  ...props
}) => {
  const [enabled, setEnabled] = useState(false);

  useEffect(() => {
    const animation = requestAnimationFrame(() => setEnabled(true));
    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, []);

  if (!enabled) return null;
  return <Droppable {...props}>{children}</Droppable>;
};

interface LogConfigFieldSelectorProps {
  availableFields: FilteredLogGroup[];
  selectedFields: LogField[];
  onFieldAdd: (field: DatabaseLogField, groupName: string, logprefix: string, index?: number) => void;
  onFieldRemove: LogFieldRemoveHandler;
  onFieldEdit: LogFieldEditHandler;
  onFieldReorder: (result: DropResult) => void;
  isLoading?: boolean;
  error?: any;
  searchTerm?: string;
  expandedGroups?: string[];
  onSearchChange?: (term: string) => void;
  onToggleGroup?: (groupName: string) => void;
}

const LogConfigFieldSelector = React.memo(function LogConfigFieldSelector({
  availableFields,
  selectedFields,
  onFieldAdd,
  onFieldRemove,
  onFieldEdit,
  onFieldReorder,
  isLoading = false,
  error = null,
  searchTerm = '',
  expandedGroups = [],
  onSearchChange,
  onToggleGroup,
}: LogConfigFieldSelectorProps) {
  // Performance monitoring
  usePerformanceMonitor('LogConfigFieldSelector');

  // Local state fallback if not provided by parent
  const [localSearchTerm, setLocalSearchTerm] = useState('');
  const [localExpandedGroups, setLocalExpandedGroups] = useState<string[]>([]);

  // Use provided state or fallback to local state
  const currentSearchTerm = onSearchChange ? searchTerm : localSearchTerm;
  const currentExpandedGroups = onToggleGroup ? expandedGroups : localExpandedGroups;

  // Debounce the search term for filtering (300ms delay)
  const debouncedSearchTerm = useDebouncedValue(currentSearchTerm, 300);

  // Initialize expanded groups when available fields change (only for local state)
  useEffect(() => {
    if (!onToggleGroup) {
      setLocalExpandedGroups(availableFields.map(g => g.name));
    }
  }, [availableFields, onToggleGroup]);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (onSearchChange) {
        onSearchChange(value);
        if (value && onToggleGroup) {
          // Expand all groups when searching
          availableFields.forEach(g => {
            if (!expandedGroups.includes(g.name)) {
              onToggleGroup(g.name);
            }
          });
        }
      } else {
        setLocalSearchTerm(value);
        if (value) {
          setLocalExpandedGroups(availableFields.map(g => g.name));
        }
      }
    },
    [onSearchChange, onToggleGroup, availableFields, expandedGroups],
  );

  const handleToggleGroup = useCallback((g: string) => {
    if (onToggleGroup) {
      onToggleGroup(g);
    } else {
      setLocalExpandedGroups((prev) =>
        prev.includes(g) ? prev.filter((n) => n !== g) : [...prev, g],
      );
    }
  }, [onToggleGroup]);

  // Filter available fields based on debounced search term with memoization
  const filteredAvailableFields = useMemo(() => {
    return availableFields.map(group => ({
      ...group,
      fields: debouncedSearchTerm
        ? group.fields.filter(
            (f: DatabaseLogField) =>
              f.caption.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
              f.field.toLowerCase().includes(debouncedSearchTerm.toLowerCase()),
          )
        : group.fields,
    })).filter(group => group.fields.length > 0);
  }, [availableFields, debouncedSearchTerm]);

  return (
    <DragDropContext onDragEnd={onFieldReorder}>
      <Box>
        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
          Field Selection
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Select and configure the data fields to include in your log output. Drag fields from the available list to add them, or use the arrow button.
        </Typography>
      </Box>
      <Grid container spacing={3} sx={{ minHeight: 500 }}>
        {/* Available fields */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%', boxShadow: 1 }}>
            <CardHeader
              title="Available Fields"
              action={
                <TextField
                  placeholder="Search fields…"
                  size="small"
                  value={currentSearchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ width: 200 }}
                />
              }
            />
            <Divider />
            <CardContent sx={{ p: 0, height: 400, overflow: 'auto' }}>
              <StrictModeDroppable droppableId="availableFields" isDropDisabled>
                {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      minHeight: 100,
                      background: snapshot.isDraggingOver
                        ? 'rgba(63,81,181,.08)'
                        : 'transparent',
                      transition: 'background-color .2s',
                    }}
                  >
                    {isLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                        <CircularProgress />
                      </Box>
                    ) : error ? (
                      <Box sx={{ p: 2 }}>
                        <Typography color="error">Error loading log fields</Typography>
                      </Box>
                    ) : filteredAvailableFields.map((g) => (
                      <Accordion
                        key={g.name}
                        expanded={currentExpandedGroups.includes(g.name)}
                        onChange={() => handleToggleGroup(g.name)}
                        disableGutters
                        className="log-config-field-accordion"
                      >
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography>{g.label}</Typography>
                        </AccordionSummary>
                        <AccordionDetails sx={{ p: 0 }}>
                          <List dense>
                            {g.fields.map((f: DatabaseLogField, fi: number) => {
                              const added = selectedFields.some(
                                (sel: LogField) =>
                                  sel.field === f.field && sel.logprefix === g.logprefix,
                              );
                              return (
                                <Draggable
                                  key={`${g.name}|${fi}|${g.logprefix}`}
                                  draggableId={`${g.name}|${fi}|${g.logprefix}`}
                                  index={fi}
                                >
                                  {(prov: DraggableProvided) => (
                                    <ListItem
                                      ref={prov.innerRef}
                                      {...prov.draggableProps}
                                      {...prov.dragHandleProps}
                                      secondaryAction={
                                        <IconButton
                                          size="small"
                                          edge="end"
                                          onClick={() =>
                                            onFieldAdd(f, g.name, g.logprefix)
                                          }
                                        >
                                          <ArrowForwardIcon />
                                        </IconButton>
                                      }
                                      sx={{
                                        cursor: 'grab',
                                        ...(added && {
                                          borderLeft: '4px solid',
                                          borderColor: 'success.main',
                                          pl: 2,
                                          backgroundColor: 'rgba(76,175,80,0.08)',
                                        })
                                      }}
                                    >
                                      <DragIndicatorIcon
                                        fontSize="small"
                                        sx={{
                                          color: 'text.secondary',
                                          mr: 1,
                                          opacity: 0.7
                                        }}
                                      />
                                      <ListItemText
                                        primary={f.caption}
                                        secondary={f.field}
                                        primaryTypographyProps={{
                                          variant: 'body2',
                                          color: 'text.primary',
                                        }}
                                        secondaryTypographyProps={{
                                          variant: 'caption',
                                          color: 'text.secondary',
                                        }}
                                      />
                                    </ListItem>
                                  )}
                                </Draggable>
                              );
                            })}
                          </List>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </StrictModeDroppable>
            </CardContent>
          </Card>
        </Grid>

        {/* Selected fields */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ height: '100%', boxShadow: 1 }}>
            <CardHeader
              title="Selected Fields"
              subheader={`${selectedFields.length} field(s) selected`}
              sx={{ pb: 1 }}
            />
            <Divider />
            <CardContent sx={{ p: 0, height: 400, overflow: 'auto', display: 'flex', flexDirection: 'column' }}>
              <StrictModeDroppable droppableId="selectedFields">
                {(provided: DroppableProvided, snapshot: DroppableStateSnapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      flexGrow: 1,
                      minHeight: 100,
                      background: snapshot.isDraggingOver
                        ? 'rgba(76,175,80,.08)'
                        : 'transparent',
                      transition: 'background-color .2s',
                    }}
                  >
                    {selectedFields.length === 0 ? (
                      <Box p={4} textAlign="center" sx={{ borderRadius: 1, backgroundColor: 'grey.50', m: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                          No fields selected. Drag fields from the left panel or use the arrow buttons to add them.
                        </Typography>
                      </Box>
                    ) : (
                      <List dense>
                        {selectedFields.map((f: LogField, idx: number) => (
                          <Draggable
                            key={`selected-${f.logprefix || ''}-${f.field}-${idx}`}
                            draggableId={`selected-${f.logprefix || ''}-${f.field}-${idx}`}
                            index={idx}
                          >
                            {(prov: DraggableProvided) => (
                              <ListItem
                                ref={prov.innerRef}
                                {...prov.draggableProps}
                                divider={idx < selectedFields.length - 1}
                              >
                                <Box
                                  {...prov.dragHandleProps}
                                  sx={{
                                    flexGrow: 1,
                                    cursor: 'grab',
                                    display: 'flex',
                                    alignItems: 'center'
                                  }}
                                >
                                  <DragIndicatorIcon
                                    fontSize="small"
                                    sx={{
                                      color: 'text.secondary',
                                      mr: 1,
                                      opacity: 0.7
                                    }}
                                  />
                                  <ListItemText
                                    primary={f.caption}
                                    secondary={
                                      <Box component="span">
                                        {f.logprefix && (
                                          <Typography
                                            component="span"
                                            variant="caption"
                                            color="primary"
                                            sx={{ mr: 1 }}
                                          >
                                            {f.logprefix}:
                                          </Typography>
                                        )}
                                        {f.field}
                                      </Box>
                                    }
                                  />
                                </Box>

                                <Box onClick={(e) => e.stopPropagation()}>
                                  <IconButton
                                    size="small"
                                    sx={{ mr: 1 }}
                                    onClick={() => onFieldEdit(f, idx)}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    onClick={() => onFieldRemove(idx)}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Box>
                              </ListItem>
                            )}
                          </Draggable>
                        ))}
                      </List>
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </StrictModeDroppable>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </DragDropContext>
  );
});

// Memoization comparison function for better performance
LogConfigFieldSelector.displayName = 'LogConfigFieldSelector';

export default LogConfigFieldSelector;